import { Id, NullableId, Paginated, Params, ServiceMethods } from '@feathersjs/feathers';
import { Application } from '../../../../../declarations';
import { Knex } from 'knex';
import { dbRawReadReporting, dbRawReadCount } from '../../../../../util/db-raw';
import { Errors } from '../../../../../errors/general';
import { techReadinessFlagsRequired } from '../../../../../constants/g9-constants';
import { boardTechReadinessFlagsRequired } from '../../../../../constants/g9-constants';

type NumberHash = { [key: number]: boolean };

type Data = SummaryData | CachedSummaryData | {}
interface SummaryData {
  districts:any,
  schools:any,
  teachers:any,
  students:any,
  classes:any
}

interface CachedSummaryData {
  summaryData:SummaryData,
  timestamp:string
}

interface ServiceOptions { }

export enum CourseType {
  EQAO_G9 = 'EQAO_G9M',
  EQAO_G10 = 'EQAO_G10L',
  EQAO_G3P = 'EQAO_G3P',
  EQAO_G6J = 'EQAO_G6J',
  EQAO_PJTQ = 'EQAO_PJTQ'
}

// to do: this should come from the twtdar
export const renderSampleAssessmentSlug = (courseType: CourseType) => {
  switch(courseType) {
    case CourseType.EQAO_G9:
      return 'G9_SAMPLE';
    case CourseType.EQAO_G10:
      return 'OSSLT_SAMPLE';
    case CourseType.EQAO_G3P:
      return 'PRIMARY_SAMPLE';
    case CourseType.EQAO_G6J:
      return 'JUNIOR_SAMPLE';
    default:
      return 'G9_SAMPLE';
  }
}

// to do: this should come from the twtdar
export const renderOperationalAssessmentSlug = (courseType: CourseType) => {
  switch(courseType) {
    case CourseType.EQAO_G9:
      return 'G9_OPERATIONAL';
    case CourseType.EQAO_G10:
      return 'OSSLT_OPERATIONAL';
    case CourseType.EQAO_G3P:
      return 'PRIMARY_OPERATIONAL';
    case CourseType.EQAO_G6J:
      return 'JUNIOR_OPERATIONAL';
    default:
      return 'G9_SAMPLE';
    }
}

export class Summary implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;

  constructor(options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
  }

  private async getData(props: any[], query: string) {
    const db:Knex = this.app.get('knexClientReadReporting');
    const res = await db.raw(query, props);
    return <any[]>res[0];
  }

  async find(params?: Params): Promise<Data[] | Paginated<Data>> {
    const db:Knex = this.app.get('knexClientReadReporting');
    const getCount = async (query: string, props?: any[]) => {
      const res = await db.raw(query)
      return res[0][0].count;
    }


    const operational = await getCount(`
      select count(0) as count
      from (
      select sc.id
      from school_classes sc
      join school_class_test_sessions scts on scts.school_class_id = sc.id and scts.slug = 'G9_OPERATIONAL' and sc.is_active = 1
      group by sc.id) t
    `);

    const sample = await getCount(`
      select count(0) as count
      from (
        select sc.id
        from school_classes sc
        join school_class_test_sessions scts on scts.school_class_id = sc.id and scts.slug = 'G9_SAMPLE' and sc.is_active = 1
        group by sc.id
      ) t
    `);

    const reports = await getCount(`
      select count(0) as count from ( select uid from student_reports group by uid) t;
    `);


    const results:any[] = [{
      ... await this.getRegistered(151), // deprecated
      sample,
      operational,
      reports,
    }];
    return results;
  }

  asyncGetTechReadiSummary() {
    return dbRawReadReporting(this.app, [], `
      select ugc.tally
           , s.name as SchName
           , s.foreign_id as SchMident
           , u.first_name as PrincipalFirstName
           , u.contact_email as PrincipalEmail
      from (
        select uid, group_id, sum(\`value\`) as tally
        from user_group_checklist ugc
        where slug in ('${techReadinessFlagsRequired.join("','")}')
        group by uid, group_id
      ) ugc
      join schools s on s.group_id = ugc.group_id
      join school_districts sd
        on sd.group_id = s.schl_dist_group_id
        and sd.is_sample = 0
      join users u on u.id = ugc.uid
      order by tally desc;
  `)
  }

  renderSDActiveFilter() {
    return '(sd.is_active = 1 and sd.is_sample = 0)'
  }
renderTWIdJoin(test_window_id: number | null, linker = 'sc.schl_dist_group_id = sd.group_id') {
    if (test_window_id === null){
      return `
        JOIN school_classes sc ON  ${linker}
        JOIN school_semesters ss ON ss.id = sc.semester_id
      `;
    }
    return `
      JOIN school_classes sc
        ON  ${linker}
      JOIN school_semesters ss
        ON ss.id = sc.semester_id
        AND ss.test_window_id = ${test_window_id}
    `;
  }

  renderDistrictsJoinQuery(test_window_id: number | null, joinQuery: string = '') {
    return `
      SELECT sd.id
      FROM (
        SELECT sd.id
        FROM school_districts sd
        ${this.renderTWIdJoin(test_window_id)}
        where ${this.renderSDActiveFilter()}
        GROUP BY sd.id
      ) sd
      ${joinQuery}
    `;
  }
  renderSchoolsJoinQuery(test_window_id: number, joinQuery: string = '') {
    return `
      SELECT s.id
      FROM (
        SELECT s.*
        FROM schools s
        JOIN school_districts sd
          on sd.group_id = s.schl_dist_group_id
          and ${this.renderSDActiveFilter()}
        ${this.renderTWIdJoin(test_window_id, 'sc.schl_group_id = s.group_id')}
        GROUP BY s.id
      ) s
      ${joinQuery}
    `;
  }
  renderTeachersJoinQuery(test_window_id: number, joinQuery: string = '') {
    return `
      SELECT urt.uid
           , s.foreign_id as s_foreign_id
           , sd.foreign_id as sd_foreign_id
      FROM schools s
      JOIN school_districts sd
        on sd.group_id = s.schl_dist_group_id
        and ${this.renderSDActiveFilter()}
      ${this.renderTWIdJoin(test_window_id, 'sc.schl_group_id = s.group_id')}
      JOIN user_roles urt
        on urt.group_id = sc.group_id
        and urt.role_type = 'schl_teacher'
        and urt.is_revoked != 1
      ${joinQuery}
      group by urt.uid
    `;
  }
  renderClassesJoinQuery(test_window_id: number, joinQuery: string = '') {
    return `
      SELECT sc.*
      FROM (
        SELECT sc2.id
             , sc2.schl_group_id
             , sc2.schl_dist_group_id
             , sc2.semester_id
             , sc2.name
             , sc2.group_id
        FROM school_classes sc2
        JOIN school_districts sd
          on sd.group_id = sc2.schl_dist_group_id
          and ${this.renderSDActiveFilter()}
          and sc2.is_active = 1
        JOIN user_roles urt
          on urt.group_id = sc2.group_id
          and urt.role_type = 'schl_student'
          and urt.is_revoked != 1
        ${this.renderTWIdJoin(test_window_id, 'sc.id = sc2.id')}
        group by sc2.id
      ) sc
      ${joinQuery}
    `;
  }
  renderStudentsJoinQuery(test_window_id: number, joinQuery: string = '') {
    const query = `
      SELECT urs.uid
           , s.foreign_id as s_foreign_id
           , sd.foreign_id as sd_foreign_id
           , sc.name as sc_name
           , sc.id as sc_id
           , ss.foreign_id as ss_foreign_id
      FROM schools s
      JOIN school_districts sd
        on sd.group_id = s.schl_dist_group_id
        and ${this.renderSDActiveFilter()}
      ${this.renderTWIdJoin(test_window_id, 'sc.schl_group_id = s.group_id')}
      JOIN user_roles urs
        on urs.group_id = sc.group_id
        and urs.role_type = 'schl_student'
      left join school_class_test_sessions scts
        on scts.school_class_id = sc.id
      left join test_attempts ta
        on ta.uid = urs.uid and ta.test_session_id = scts.test_session_id
      and urs.is_revoked = 1
      ${joinQuery}
      ${joinQuery ? ' and ' : ' where '} (ta.uid = urs.uid OR urs.is_revoked != 1)
      group by urs.uid
    `;
    return query;
  }

  async getRegistered(test_window_id: number) {
    return {
      districts: await dbRawReadCount(this.app, this.renderDistrictsJoinQuery(test_window_id)),
      schools: await dbRawReadCount(this.app, this.renderSchoolsJoinQuery(test_window_id)),
      teachers: await dbRawReadCount(this.app, this.renderTeachersJoinQuery(test_window_id)),
      students: await dbRawReadCount(this.app, this.renderStudentsJoinQuery(test_window_id)),
      classes: await dbRawReadCount(this.app, this.renderClassesJoinQuery(test_window_id))
    };
  }

  async getBoardCompletedTech(test_window_id: number) {
    const query = this.renderBoardCompletedTechQuery(test_window_id);
    const records = await dbRawReadReporting(this.app, [], query);
    return this.parseUnionRecordsToUserGroupCount(records,test_window_id)
  }

  async getCompletedTech(test_window_id: number,courseType:CourseType) {
    const records = await dbRawReadReporting(this.app, [], `
      ${this.renderCompletedTechQuery(test_window_id,courseType)}
    `);
    return this.parseUnionRecordsToUserGroupCount(records,test_window_id)
  }

  async renderStuReportsQuery(test_window_id: number, assessmentSlug: string){
   return dbRawReadReporting(this.app, [], `
   select sc.schl_dist_group_id
          , sc.schl_group_id
          , ur.uid as teacher_uid
          , ta.uid as student_uid
          , sc.group_id as sc_group_id
      from test_attempts ta
      join test_sessions ts
        on ts.id = ta.test_session_id
        and ts.test_window_id = ${test_window_id}
      join school_class_test_sessions scts
        on scts.test_session_id = ta.test_session_id
        and scts.slug = '${assessmentSlug}'
      ${this.renderTWIdJoin(test_window_id, 'sc.id = scts.school_class_id')}
      JOIN school_districts sd
        on sd.group_id = sc.schl_dist_group_id
        and ${this.renderSDActiveFilter()}
      join student_reports sr on sr.attempt_id = ta.id and sr.is_isr=1 and sr.is_revoked != 1
      left join user_roles ur on ur.group_id = sc.group_id and ur.is_revoked != 1 AND ur.role_type = 'schl_teacher'
      WHERE ta.started_on IS NOT NULL
      group by ta.uid
  ;`);
  }

  async getStuReportsAccessed(test_window_id: number, courseType: CourseType) {
    // this.renderSchoolsJoinQuery(test_window_id)
    const records = await this.renderStuReportsQuery(test_window_id, renderOperationalAssessmentSlug(courseType));
    return this.parseRecordsToUserGroupCount(records);
  }
  async getStuReportsAccessedRecords(test_window_id: number,record_type:string, courseType: CourseType) {
    // this.renderSchoolsJoinQuery(test_window_id)
    const records = await this.renderStuReportsQuery(test_window_id, renderOperationalAssessmentSlug(courseType));
    return this.parseRecordsToUserGroupCountRecords(records,false,record_type);
  }
  async getCompletedSample(test_window_id: number, courseType: CourseType) {
    const records = await dbRawReadReporting(this.app, [], this.renderCompletedAssessmentQuery(test_window_id, renderSampleAssessmentSlug(courseType)));
    return this.parseRecordsToUserGroupCount(records, true);
  }

  renderCompletedAssessmentQuery(test_window_id: number, assessmentSlug:string) {
    return `
      select sc.schl_dist_group_id
            , sc.schl_group_id
            , NULL as teacher_uid
            , ta.uid as student_uid
            , sc.id as sc_id
            , sc.group_id as sc_group_id
            , sc.id
      from test_attempts ta
      join test_sessions ts
        on ts.id = ta.test_session_id
        and ts.test_window_id = ${test_window_id}
      join school_class_test_sessions scts
        on scts.test_session_id = ta.test_session_id
        and scts.slug = '${assessmentSlug}'
      ${this.renderTWIdJoin(test_window_id, 'sc.id = scts.school_class_id')}
      JOIN school_districts sd
        on sd.group_id = sc.schl_dist_group_id
        and ${this.renderSDActiveFilter()}
      WHERE ta.is_closed = 1
        AND EXISTS (
          SELECT 1 FROM test_attempt_question_responses taqr
          WHERE taqr.test_attempt_id = ta.id AND taqr.is_nr = 0
          LIMIT 1
        )
    `
  }

  renderCompletedAssessmentQueryPJ(test_window_id: number, assessmentSlug:string) {
    return `
      select /*+ MAX_EXECUTION_TIME(1440000000)*/ sc.schl_dist_group_id
            , sc.schl_group_id
            , NULL as teacher_uid
            , ta.uid as student_uid
            , sc.id as sc_id
            , sc.group_id as sc_group_id
            , sc.id
      from test_attempts ta
      join test_attempt_question_responses taqr USE INDEX (attempt)
        on taqr.test_attempt_id = ta.id
      join test_sessions ts
        on ts.id = ta.test_session_id
        and ts.test_window_id = ${test_window_id}
      join school_class_test_sessions scts
        on scts.test_session_id = ta.test_session_id
        and scts.slug = '${assessmentSlug}'
      ${this.renderTWIdJoin(test_window_id, 'sc.id = scts.school_class_id')}
      JOIN school_districts sd
        on sd.group_id = sc.schl_dist_group_id
        and ${this.renderSDActiveFilter()}
      WHERE taqr.is_nr = 0
      AND (taqr.section_id = 0 OR taqr.section_id = 1 OR taqr.section_id = 2 OR taqr.section_id = 3)
      group by sc.id, ta.uid
      having group_concat(distinct taqr.section_id order by taqr.section_id) = '0,1,2,3'
    `
  }

  renderCompletedAssessmentQueryG9(test_window_id: number, assessmentSlug:string) {
    return `
      select /*+ MAX_EXECUTION_TIME(1440000000)*/ sc.schl_dist_group_id
            , sc.schl_group_id
            , NULL as teacher_uid
            , ta.uid as student_uid
            , sc.id as sc_id
            , sc.group_id as sc_group_id
            , sc.id
      from test_attempts ta
      join test_attempt_question_responses taqr USE INDEX (attempt)
        on taqr.test_attempt_id = ta.id
      join test_sessions ts
        on ts.id = ta.test_session_id
        and ts.test_window_id = ${test_window_id}
      join school_class_test_sessions scts
        on scts.test_session_id = ta.test_session_id
        and scts.slug = '${assessmentSlug}'
      ${this.renderTWIdJoin(test_window_id, 'sc.id = scts.school_class_id')}
      JOIN school_districts sd
        on sd.group_id = sc.schl_dist_group_id
        and ${this.renderSDActiveFilter()}
      WHERE taqr.is_nr = 0
      AND (taqr.section_id = 0 OR taqr.section_id = 2)
      group by sc.id, ta.uid
      having group_concat(distinct taqr.section_id order by taqr.section_id) = '0,2'
    `
  }

  renderCompletedAssessmentQueryOSSLT(test_window_id: number, assessmentSlug:string) {
    return `
      select /*+ MAX_EXECUTION_TIME(1440000000)*/ sc.schl_dist_group_id
            , sc.schl_group_id
            , NULL as teacher_uid
            , ta.uid as student_uid
            , sc.id as sc_id
            , sc.group_id as sc_group_id
            , sc.id
      from test_attempts ta
      join test_attempt_question_responses taqr USE INDEX (question_attempt,attempt)
        on taqr.test_attempt_id = ta.id
      join test_sessions ts
        on ts.id = ta.test_session_id
        and ts.test_window_id = ${test_window_id}
      join school_class_test_sessions scts
        on scts.test_session_id = ta.test_session_id
        and scts.slug = '${assessmentSlug}'
      ${this.renderTWIdJoin(test_window_id, 'sc.id = scts.school_class_id')}
      JOIN school_districts sd
        on sd.group_id = sc.schl_dist_group_id
        and ${this.renderSDActiveFilter()}
      join eqao_model.question_subsession_mapping_osslt qsmo
        on qsmo.question_id = taqr.test_question_id
      WHERE taqr.is_nr = 0
      group by sc.id, ta.uid
      having count (distinct qsmo.sub_session) >= 2
    `
  }

  // where slug in ('${boardTechReadinessFlagsRequired.join("','")}')

  renderBoardCompletedTechQuery(test_window_id: number | null) {
    return `SELECT sdugc.*
            FROM (
              SELECT sd.id, sd.group_id as schl_dist_group_id, s.group_id as schl_group_id
                FROM (
                  select uid, group_id, sum(\`value\`) as tally
                  from user_group_checklist ugc

                  group by uid, ugc.group_id
                ) ugc
              JOIN school_districts sd
                on sd.group_id = ugc.group_id
                and ${this.renderSDActiveFilter()}
              join schools s
                on sd.group_id = s.schl_dist_group_id
              where tally >= ${boardTechReadinessFlagsRequired.length}
              group by ugc.group_id
            ) sdugc
            ${this.renderTWIdJoin(test_window_id, 'sc.schl_group_id = sdugc.schl_group_id')}
            group by sdugc.schl_group_id;`
  }

  renderCompletedTechQuery(test_window_id: number | null, courseType:CourseType) {

    // return `SELECT sugc.*
    //         FROM (
    //           SELECT s.id, schl_group_id, s.schl_dist_group_id
    //             FROM (
    //               select uid, group_id as schl_group_id, sum(\`value\`) as tally
    //               from user_group_checklist ugc
    //               where slug in ('${techReadinessFlagsRequired.join("','")}')
    //               group by uid, ugc.group_id
    //             ) ugc
    //           join schools s on s.group_id = ugc.schl_group_id
    //           JOIN school_districts sd
    //             on sd.group_id = s.schl_dist_group_id
    //             and ${this.renderSDActiveFilter()}
    //           where tally >= ${techReadinessFlagsRequired.length}
    //           group by ugc.schl_group_id
    //         ) sugc
    //         ${this.renderTWIdJoin(test_window_id, 'sc.schl_group_id = sugc.schl_group_id')}
    //         group by sugc.schl_group_id;`

    let ugcSlug;
    switch (courseType) {
      case CourseType.EQAO_G9:
        ugcSlug = 'tech_redi_go_mode';
        break;
      case CourseType.EQAO_G10:
        ugcSlug = 'tech_redi_go_mode_osslt';
        break;
      case CourseType.EQAO_G3P:
        ugcSlug = 'tech_redi_go_mode_primary';
        break;
      case CourseType.EQAO_G6J:
        ugcSlug = 'tech_redi_go_mode_junior';
        break;
    }

    return `SELECT sugc.*
            FROM (
              SELECT s.id, schl_group_id, s.schl_dist_group_id
                FROM (
                  select uid, group_id as schl_group_id, value as val
                  from user_group_checklist ugc
                  where slug = '${ugcSlug}'
                  group by uid, ugc.group_id
                ) ugc
              join schools s on s.group_id = ugc.schl_group_id
              JOIN school_districts sd
                on sd.group_id = s.schl_dist_group_id
                and ${this.renderSDActiveFilter()}
              where val = 1
              group by ugc.schl_group_id
            ) sugc
            ${this.renderTWIdJoin(test_window_id, 'sc.schl_group_id = sugc.schl_group_id')}
            group by sugc.schl_group_id;`
  }

  async getCompletedSampleAndTech(test_window_id: number, courseType: CourseType) {
    const TECH_READI = 'TECH_READI';
    const SAMPLE_COMPLETE = 'SAMPLE_COMPLETE';
    const recordsTechQuery = await dbRawReadReporting(this.app, [], this.renderCompletedTechQuery(test_window_id,courseType));
    type FlagMap = {[key:string]:boolean};
    type MultiFlagMap = {[key:string]:{[key:string]:boolean}};
    const districtsHash:MultiFlagMap = {};
    const schoolsHash:MultiFlagMap = {};
    const getIdentProp = (obj:MultiFlagMap, id:string, props:string[]) => {
      if (obj[id]){
        let isActivated = true;
        props.forEach(prop => {
          if (!obj[id][prop]){
            isActivated = false;
          }
        })
        return isActivated;
      }
      return false;
    }
    const markIdentProp = (obj:MultiFlagMap, id:string, prop:string, val:boolean=true) => {
      if (!obj[id]){ obj[id] = {}; }
      obj[id][prop] = val;
    }
    recordsTechQuery.forEach(record => {
      markIdentProp(districtsHash, record.schl_dist_group_id, TECH_READI);
      markIdentProp(schoolsHash, record.schl_group_id, TECH_READI);
    })
    const recordsSample = await dbRawReadReporting(this.app, [], this.renderCompletedAssessmentQuery(test_window_id, renderSampleAssessmentSlug(courseType)));
    const schoolClassesHash:FlagMap = {};
    const studentsHash:FlagMap = {};
    recordsSample.forEach(record => {
      if (getIdentProp(schoolsHash, record.schl_group_id, [TECH_READI])){
        schoolClassesHash[record.sc_group_id] = true;
        studentsHash[record.student_uid] = true;
        markIdentProp(districtsHash, record.schl_dist_group_id, SAMPLE_COMPLETE);
        markIdentProp(schoolsHash, record.schl_group_id, SAMPLE_COMPLETE);
      }
    })

    const pullFlaggedHashIds = (obj:MultiFlagMap) => {
      const arr:string[] = [];
      Object.keys(obj).forEach(id => {
        if (getIdentProp(obj, id, [TECH_READI, SAMPLE_COMPLETE])){
          arr.push(id);
        }
      })
      return arr;
    }

    const districts = pullFlaggedHashIds(districtsHash);
    const schools = pullFlaggedHashIds(schoolsHash);
    const students = Object.keys(studentsHash);
    const classes = Object.keys(schoolClassesHash);
    const teachers = await this.lookupTeachersByClasses(classes);

    return {
      districts: districts.length,
      schools: schools.length,
      teachers: teachers.length,
      students: students.length,
      classes: classes.length
    };
  }

  async getCompletedSampleAndTechRecords(test_window_id: number,record_type:string, courseType: CourseType):Promise<any[]> {
    const TECH_READI = 'TECH_READI';
    const SAMPLE_COMPLETE = 'SAMPLE_COMPLETE';
    const recordsTechQuery = await dbRawReadReporting(this.app, [], this.renderCompletedTechQuery(test_window_id,courseType));
    type FlagMap = {[key:string]:boolean};
    type MultiFlagMap = {[key:string]:{[key:string]:boolean}};
    const districtsHash:MultiFlagMap = {};
    const schoolsHash:MultiFlagMap = {};
    const getIdentProp = (obj:MultiFlagMap, id:string, props:string[]) => {
      if (obj[id]){
        let isActivated = true;
        props.forEach(prop => {
          if (!obj[id][prop]){
            isActivated = false;
          }
        })
        return isActivated;
      }
      return false;
    }
    const markIdentProp = (obj:MultiFlagMap, id:string, prop:string, val:boolean=true) => {
      if (!obj[id]){ obj[id] = {}; }
      obj[id][prop] = val;
    }
    recordsTechQuery.forEach(record => {
      markIdentProp(districtsHash, record.schl_dist_group_id, TECH_READI);
      markIdentProp(schoolsHash, record.schl_group_id, TECH_READI);
    })

    // Use optimized query with EXISTS instead of JOIN
    const recordsSample = await dbRawReadReporting(this.app, [], this.renderCompletedAssessmentQuery(test_window_id, renderSampleAssessmentSlug(courseType)));
    const schoolClassesHash:FlagMap = {};
    const studentsHash:FlagMap = {};
    recordsSample.forEach(record => {
      if (getIdentProp(schoolsHash, record.schl_group_id, [TECH_READI])){
        schoolClassesHash[record.sc_group_id] = true;
        studentsHash[record.student_uid] = true;
        markIdentProp(districtsHash, record.schl_dist_group_id, SAMPLE_COMPLETE);
        markIdentProp(schoolsHash, record.schl_group_id, SAMPLE_COMPLETE);
      }
    })

    const pullFlaggedHashIds = (obj:MultiFlagMap) => {
      const arr:string[] = [];
      Object.keys(obj).forEach(id => {
        if (getIdentProp(obj, id, [TECH_READI, SAMPLE_COMPLETE])){
          arr.push(id);
        }
      })
      return arr;
    }

    const districts = pullFlaggedHashIds(districtsHash);
    const schools = pullFlaggedHashIds(schoolsHash);
    const students = Object.keys(studentsHash);
    const classes = Object.keys(schoolClassesHash);
    const teachers = await this.lookupTeachersByClasses(classes);

    switch (record_type) {
      case 'Districts': return districts;
      case 'Schools': return schools;
      case 'Students': return students;
      case 'Classes': return classes;
      case 'Teachers': return teachers;
    }
    return []
    // return {
    //   districts: districts.length,
    //   schools: schools.length,
    //   teachers: teachers.length,
    //   students: students.length,
    //   classes: classes.length
    // };
  }
  async renderStartedOperQuery(test_window_id:number, assessmentSlug: string){
    return dbRawReadReporting(this.app, [], `
      select sc.schl_dist_group_id
          , sc.schl_group_id
          , NULL as teacher_uid
          , ta.uid as student_uid
          , sc.group_id as sc_group_id
      from test_attempts ta
      join test_sessions ts
        on ts.id = ta.test_session_id
        and ts.test_window_id = ${test_window_id}
      join school_class_test_sessions scts
        on scts.test_session_id = ta.test_session_id
        and scts.slug = '${assessmentSlug}'
      ${this.renderTWIdJoin(test_window_id, 'sc.id = scts.school_class_id')}
      JOIN school_districts sd
        on sd.group_id = sc.schl_dist_group_id
        and ${this.renderSDActiveFilter()}
      WHERE ta.started_on IS NOT NULL
      group by ta.uid
  ;`);
  }
  async getStartedOper(test_window_id: number, courseType: CourseType) {
    const records = await this.renderStartedOperQuery(test_window_id, renderOperationalAssessmentSlug(courseType) );
    return this.parseRecordsToUserGroupCount(records, true);
  }
  async getStartedOperRecords(test_window_id: number,record_type:string, courseType: CourseType):Promise<any[]> {
    const records = await this.renderStartedOperQuery(test_window_id, renderOperationalAssessmentSlug(courseType));
    return this.parseRecordsToUserGroupCountRecords(records, true,record_type);
  }

  async getCompletedOper(test_window_id: number, courseType: CourseType) {
    let records:any = [];
    // return records;
    if (courseType === CourseType.EQAO_G10) {
      records = await dbRawReadReporting(this.app, [], this.renderCompletedAssessmentQueryOSSLT(test_window_id, renderOperationalAssessmentSlug(courseType)));
    } else if (courseType === CourseType.EQAO_G9) {
      records = await dbRawReadReporting(this.app, [], this.renderCompletedAssessmentQueryG9(test_window_id, renderOperationalAssessmentSlug(courseType)));
    } else if (courseType === CourseType.EQAO_G3P || courseType === CourseType.EQAO_G6J) {
      records = await dbRawReadReporting(this.app, [], this.renderCompletedAssessmentQueryPJ(test_window_id, renderOperationalAssessmentSlug(courseType)));
    }
    return this.parseRecordsToUserGroupCount(records, true);
  }
  async getCompletedOperRecords(test_window_id: number,record_type:string, courseType: CourseType) {
    let records = [];
    if (courseType === CourseType.EQAO_G10) {
      records = await dbRawReadReporting(this.app, [], this.renderCompletedAssessmentQueryOSSLT(test_window_id, renderOperationalAssessmentSlug(courseType)));
    } else if (courseType === CourseType.EQAO_G9) {
      records = await dbRawReadReporting(this.app, [], this.renderCompletedAssessmentQueryG9(test_window_id, renderOperationalAssessmentSlug(courseType)));
    } else if (courseType === CourseType.EQAO_G3P || courseType === CourseType.EQAO_G6J) {
      records = await dbRawReadReporting(this.app, [], this.renderCompletedAssessmentQueryPJ(test_window_id, renderOperationalAssessmentSlug(courseType)));
    }
    return this.parseRecordsToUserGroupCountRecords(records, true,record_type);
  }

  async parseUnionRecordsToUserGroupCount(records: any[], test_window_id: number) {
    if (records.length === 0){
      return {
        districts: 0,
        schools: 0,
        teachers: 0,
        students: 0,
        classes: 0,
      }
    }
    const districtsHash: NumberHash = {};
    const schoolsHash: NumberHash = {};
    records.forEach(record => {
      districtsHash[record.schl_dist_group_id] = true;
      schoolsHash[record.schl_group_id] = true;
    });
    const districts = Object.keys(districtsHash);
    const schools = Object.keys(schoolsHash);

    return {
      districts: districts.length,
      schools: schools.length,
      teachers: await dbRawReadCount(this.app, this.renderTeachersJoinQuery(test_window_id, `
        where s.group_id IN (${schools.join(',')})
      `)),
      students: await dbRawReadCount(this.app, this.renderStudentsJoinQuery(test_window_id, `
        where s.group_id IN (${schools.join(',')})
      `)),
      classes: await dbRawReadCount(this.app, this.renderClassesJoinQuery(test_window_id, `
        where sc.schl_group_id IN (${schools.join(',')})
      `))
    };
  }
  async parseRecordsToUserGroup(records: any[], lookupTeacherByClasses: boolean = false) {
    const districtsHash: NumberHash = {};
    const schoolsHash: NumberHash = {};
    const schoolClassesHash: NumberHash = {};
    const teacherHash: NumberHash = {};
    const studentHash: NumberHash = {};
    records.forEach(record => {
      districtsHash[record.schl_dist_group_id] = true;
      schoolsHash[record.schl_group_id] = true;
      teacherHash[record.teacher_uid] = true;
      studentHash[record.student_uid] = true;
      schoolClassesHash[record.sc_group_id] = true;
    });
    const districts = Object.keys(districtsHash);
    const schools = Object.keys(schoolsHash);
    let teachers = Object.keys(teacherHash);
    const students = Object.keys(studentHash);
    const classes = Object.keys(schoolClassesHash);

    if (lookupTeacherByClasses){
      teachers = await this.lookupTeachersByClasses(classes);
    }

    return {
      districts,
      schools,
      teachers,
      students,
      classes
    };
  }
  async parseRecordsToUserGroupCount(records: any[], lookupTeacherByClasses: boolean = false) {

    return {
      districts: await (await this.parseRecordsToUserGroup(records,lookupTeacherByClasses)).districts.length,
      schools: await (await this.parseRecordsToUserGroup(records,lookupTeacherByClasses)).schools.length,
      teachers: await (await this.parseRecordsToUserGroup(records,lookupTeacherByClasses)).teachers.length,
      students: await (await this.parseRecordsToUserGroup(records,lookupTeacherByClasses)).students.length,
      classes: await (await this.parseRecordsToUserGroup(records,lookupTeacherByClasses)).classes.length
    };
  }
  async parseRecordsToUserGroupCountRecords(records: any[], lookupTeacherByClasses: boolean = false,record_type:string):Promise<any[]> {
    switch (record_type) {
      case 'Districts': return await (await this.parseRecordsToUserGroup(records,lookupTeacherByClasses)).districts;
      case 'Schools': return await (await this.parseRecordsToUserGroup(records,lookupTeacherByClasses)).schools;
      case 'Students': return await (await this.parseRecordsToUserGroup(records,lookupTeacherByClasses)).students;
      case 'Classes': return await (await this.parseRecordsToUserGroup(records,lookupTeacherByClasses)).classes;
      case 'Teachers': return await (await this.parseRecordsToUserGroup(records,lookupTeacherByClasses)).teachers;
    }
    return[]
  }

  private async lookupTeachersByClasses(classes:string[]){
    if (classes.length === 0){
      return [];
    }
    else{
      const teacherRoleRecords = await dbRawReadReporting(this.app, [classes], `
        SELECT urt.uid
        FROM school_classes sc
        JOIN user_roles urt
          on urt.group_id = sc.group_id
          and urt.role_type = 'schl_teacher'
          and urt.is_revoked != 1
        WHERE sc.group_id IN (?)
        GROUP BY urt.uid
      ;`)
      return teacherRoleRecords.map(r => r.uid);
    }
  }

  private async readSummaryDataFromCache(test_window_id: number, col: string) {
    const summaryCache = await this.app
      .service('db/read/test-controller-dashboard-cache')
      .db()
      .where('test_window_id', test_window_id)
      .orderBy('timestamp', 'desc')
      .limit(1)

    const summary = summaryCache[0];

    const summaryEntry = summary ? JSON.parse(summary.summary_cache) : {};

    return {summaryData: summaryEntry[col], timestamp: summary?.timestamp};
  }


  async get(id: Id, params?: Params): Promise<Data> {
    if (params && params.query) {
      const test_window_id = +params.query.test_window_id;
      if (test_window_id === NaN) {
        throw new Errors.BadRequest('MISSING_FS_ID');
      }

      const courseType = params.query.type_slug;

      if (params.query.bustCache) {
        switch (params.query.col) {
          case 'Registered': return this.getRegistered(test_window_id);
          case 'BoardCompletedTech': return this.getBoardCompletedTech(test_window_id);
          case 'CompletedTech': return this.getCompletedTech(test_window_id,courseType);
          case 'CompletedSample': return this.getCompletedSample(test_window_id, courseType);
          case 'CompletedSampleAndTech': return this.getCompletedSampleAndTech(test_window_id, courseType);
          case 'StartedOper': return this.getStartedOper(test_window_id, courseType);
          case 'CompletedOper': return this.getCompletedOper(test_window_id, courseType);
          case 'StuReportsAccessed': return this.getStuReportsAccessed(test_window_id, courseType);
        }
      }
      return await this.readSummaryDataFromCache(test_window_id, params.query.col);
    }
    return {};
  }

  async create(data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  async update(id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  async patch(id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  async remove(id: NullableId, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }
}
